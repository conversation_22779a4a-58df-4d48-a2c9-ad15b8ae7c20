# System Patterns

This file documents recurring patterns and standards used in the project.
2025-08-25 10:12:35 - Log of updates made.

*

## Coding Patterns

* Supabase client initialization through singleton pattern
* React Query for data fetching and caching
* Custom hooks for role-based access control

## Architectural Patterns

* Mobile/web client separation with shared Supabase backend
* Real-time updates through Supabase subscriptions
* Centralized notification service abstraction

## Testing Patterns

* Jest/Testing Library for UI components
* Mock service worker for API testing
* Role-based test scenarios