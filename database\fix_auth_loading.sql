-- Fix Authentication Loading Issue
-- This fixes the profile fetching problem causing infinite loading
-- Run this in Supabase SQL Editor

-- First, let's check what policies currently exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'users';

-- Drop ALL existing policies on users table to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Users can create own profile" ON users;
DROP POLICY IF EXISTS "Staff can view all users" ON users;
DROP POLICY IF EXISTS "Admins can create users" ON users;
DROP POLICY IF EXISTS "Admins can update user roles" ON users;

-- Create simple, working policies

-- 1. Allow users to view their own profile (ESSENTIAL for login to work)
CREATE POLICY "users_select_own" ON users
    FOR SELECT 
    USING (auth.uid() = id);

-- 2. Allow users to update their own profile
CREATE POLICY "users_update_own" ON users
    FOR UPDATE 
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- 3. Allow users to create their own profile during signup
CREATE POLICY "users_insert_own" ON users
    FOR INSERT 
    WITH CHECK (auth.uid() = id AND role = 'patient');

-- 4. Allow staff/dentists/admins to view all users
CREATE POLICY "staff_select_all" ON users
    FOR SELECT 
    USING (
        EXISTS (
            SELECT 1 FROM users u2
            WHERE u2.id = auth.uid()
            AND u2.role IN ('staff', 'dentist', 'admin')
        )
    );

-- 5. Allow admins to create any user
CREATE POLICY "admin_insert_any" ON users
    FOR INSERT 
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users u2
            WHERE u2.id = auth.uid()
            AND u2.role = 'admin'
        )
    );

-- 6. Allow admins to update any user
CREATE POLICY "admin_update_any" ON users
    FOR UPDATE 
    USING (
        EXISTS (
            SELECT 1 FROM users u2
            WHERE u2.id = auth.uid()
            AND u2.role = 'admin'
        )
    );

-- Verify the policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY policyname;
