import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text, Card } from 'react-native-paper';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export const DebugAuth: React.FC = () => {
  const { user, profile, session } = useAuth();
  const [testResult, setTestResult] = useState<string>('');

  const testSupabaseConnection = async () => {
    try {
      setTestResult('Testing connection...');
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        setTestResult(`Connection error: ${error.message}`);
      } else {
        setTestResult(`Connection OK. Session: ${data.session ? 'Yes' : 'No'}`);
      }
    } catch (error) {
      setTestResult(`Connection failed: ${error}`);
    }
  };

  const testSignOut = async () => {
    try {
      setTestResult('Testing sign out...');
      const { error } = await supabase.auth.signOut();
      if (error) {
        setTestResult(`Sign out error: ${error.message}`);
      } else {
        setTestResult('Sign out successful');
      }
    } catch (error) {
      setTestResult(`Sign out failed: ${error}`);
    }
  };

  return (
    <Card style={styles.container}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.title}>Debug Auth</Text>
        
        <Text variant="bodySmall" style={styles.info}>
          User: {user ? 'Logged in' : 'Not logged in'}
        </Text>
        <Text variant="bodySmall" style={styles.info}>
          Profile: {profile ? profile.name : 'No profile'}
        </Text>
        <Text variant="bodySmall" style={styles.info}>
          Session: {session ? 'Active' : 'None'}
        </Text>
        
        <View style={styles.buttons}>
          <Button mode="outlined" onPress={testSupabaseConnection} style={styles.button}>
            Test Connection
          </Button>
          <Button mode="outlined" onPress={testSignOut} style={styles.button}>
            Test Sign Out
          </Button>
        </View>
        
        {testResult ? (
          <Text variant="bodySmall" style={styles.result}>
            {testResult}
          </Text>
        ) : null}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    elevation: 2,
  },
  title: {
    marginBottom: 16,
    fontWeight: 'bold',
  },
  info: {
    marginBottom: 8,
    color: '#666',
  },
  buttons: {
    flexDirection: 'row',
    marginTop: 16,
    marginBottom: 16,
  },
  button: {
    marginRight: 8,
    flex: 1,
  },
  result: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
});
