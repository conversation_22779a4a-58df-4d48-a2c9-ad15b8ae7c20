-- Create Missing User Profile
-- This creates a user profile for users who exist in auth.users but not in public.users
-- Run this in Supabase SQL Editor

-- First, let's see which users exist in auth but not in public.users
SELECT 'Users in auth.users but missing from public.users:' as info;
SELECT 
    au.id,
    au.email,
    au.created_at as auth_created_at
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL
ORDER BY au.created_at DESC;

-- Create profiles for missing users
-- This will create a basic patient profile for any user missing from public.users
INSERT INTO public.users (id, name, email, role, status)
SELECT 
    au.id,
    COALESCE(
        au.raw_user_meta_data->>'name',
        au.raw_user_meta_data->>'full_name', 
        split_part(au.email, '@', 1)
    ) as name,
    au.email,
    'patient' as role,
    'active' as status
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL
  AND au.email IS NOT NULL;

-- Verify the profiles were created
SELECT 'All users now in public.users:' as info;
SELECT id, name, email, role, status, created_at 
FROM public.users 
ORDER BY created_at DESC;
