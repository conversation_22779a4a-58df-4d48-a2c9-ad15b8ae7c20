import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Alert, Paper } from '@mui/material';
import { supabase } from '../lib/supabase';

export const DebugSignup: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('testpassword123');
  const [name, setName] = useState('Test User');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testSignup = async () => {
    setLoading(true);
    setResult('');

    try {
      console.log('Starting signup test...');
      
      // Step 1: Test Supabase auth signup
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      });

      console.log('Auth signup result:', { authData, authError });

      if (authError) {
        setResult(`Auth Error: ${authError.message}`);
        setLoading(false);
        return;
      }

      if (!authData.user) {
        setResult('Auth successful but no user data returned');
        setLoading(false);
        return;
      }

      // Step 2: Test profile creation
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          name,
          email,
          role: 'patient',
        });

      console.log('Profile creation result:', { profileError });

      if (profileError) {
        setResult(`Profile Error: ${profileError.message}\nDetails: ${JSON.stringify(profileError, null, 2)}`);
      } else {
        setResult('✅ Success! User created and profile added successfully.');
      }

    } catch (error: any) {
      console.error('Test error:', error);
      setResult(`Unexpected Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    setResult('');

    try {
      // Test basic connection
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        setResult(`Connection Error: ${error.message}`);
      } else {
        setResult('✅ Database connection successful!');
      }
    } catch (error: any) {
      setResult(`Connection Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Debug Signup Test
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          margin="normal"
        />
      </Box>

      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          onClick={testSignup}
          disabled={loading}
        >
          {loading ? 'Testing...' : 'Test Signup'}
        </Button>
        <Button
          variant="outlined"
          onClick={testConnection}
          disabled={loading}
        >
          Test Connection
        </Button>
      </Box>

      {result && (
        <Alert 
          severity={result.includes('✅') ? 'success' : 'error'}
          sx={{ mt: 2 }}
        >
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {result}
          </pre>
        </Alert>
      )}
    </Paper>
  );
};
