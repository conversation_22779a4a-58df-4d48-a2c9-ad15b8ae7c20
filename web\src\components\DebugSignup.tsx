import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Alert, Paper } from '@mui/material';
import { supabase } from '../lib/supabase';

export const DebugSignup: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('testpassword123');
  const [name, setName] = useState('Test User');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testSignup = async () => {
    setLoading(true);
    setResult('Testing signup process...\n');

    try {
      console.log('Starting signup test...');

      // Step 1: Test Supabase connection first
      setResult(prev => prev + '1. Testing Supabase connection...\n');
      const { data: testData, error: testError } = await supabase.from('users').select('count').limit(1);

      if (testError) {
        setResult(prev => prev + `❌ Connection failed: ${testError.message}\n`);
        setLoading(false);
        return;
      }

      setResult(prev => prev + '✅ Connection successful\n');

      // Step 2: Test Supabase auth signup
      setResult(prev => prev + '2. Testing auth signup...\n');
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      });

      console.log('Auth signup result:', { authData, authError });

      if (authError) {
        setResult(prev => prev + `❌ Auth Error: ${authError.message}\n`);
        if (authError.message.includes('email')) {
          setResult(prev => prev + 'Hint: Check if email confirmation is required in Supabase settings\n');
        }
        setLoading(false);
        return;
      }

      if (!authData.user) {
        setResult(prev => prev + '❌ Auth successful but no user data returned\n');
        setLoading(false);
        return;
      }

      setResult(prev => prev + `✅ Auth user created: ${authData.user.id}\n`);
      setResult(prev => prev + `   Email confirmed: ${authData.user.email_confirmed_at ? 'Yes' : 'No'}\n`);

      // Step 3: Test profile creation
      setResult(prev => prev + '3. Testing profile creation...\n');
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          name,
          email,
          role: 'patient',
        });

      console.log('Profile creation result:', { profileError });

      if (profileError) {
        setResult(prev => prev + `❌ Profile Error: ${profileError.message}\n`);
        setResult(prev => prev + `   Error Code: ${profileError.code}\n`);
        setResult(prev => prev + `   Details: ${profileError.details}\n`);
        if (profileError.message.includes('policy')) {
          setResult(prev => prev + 'Hint: RLS policy may be blocking user creation. Run the fix_user_signup_policy.sql\n');
        }
      } else {
        setResult(prev => prev + '✅ Profile created successfully!\n');
        setResult(prev => prev + '🎉 Complete success! User account fully created.\n');
      }

    } catch (error: any) {
      console.error('Test error:', error);
      setResult(prev => prev + `❌ Unexpected Error: ${error.message}\n`);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    setResult('');

    try {
      // Test basic connection
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        setResult(`Connection Error: ${error.message}`);
      } else {
        setResult('✅ Database connection successful!');
      }
    } catch (error: any) {
      setResult(`Connection Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Debug Signup Test
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          margin="normal"
        />
      </Box>

      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          onClick={testSignup}
          disabled={loading}
        >
          {loading ? 'Testing...' : 'Test Signup'}
        </Button>
        <Button
          variant="outlined"
          onClick={testConnection}
          disabled={loading}
        >
          Test Connection
        </Button>
      </Box>

      {result && (
        <Alert 
          severity={result.includes('✅') ? 'success' : 'error'}
          sx={{ mt: 2 }}
        >
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {result}
          </pre>
        </Alert>
      )}
    </Paper>
  );
};
