import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get appointments scheduled for tomorrow
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowDate = tomorrow.toISOString().split('T')[0]

    const { data: appointments, error } = await supabaseClient
      .from('appointments')
      .select(`
        id,
        date,
        start_time,
        users!patient_id (
          id,
          name,
          email
        )
      `)
      .eq('date', tomorrowDate)
      .eq('status', 'scheduled')

    if (error) {
      throw error
    }

    // Create reminder notifications for each appointment
    const notifications = appointments?.map(appointment => ({
      user_id: appointment.users.id,
      appointment_id: appointment.id,
      type: 'reminder',
      message: `Reminder: You have an appointment tomorrow at ${appointment.start_time}`,
      status: 'pending'
    })) || []

    if (notifications.length > 0) {
      const { error: insertError } = await supabaseClient
        .from('notifications')
        .insert(notifications)

      if (insertError) {
        throw insertError
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        reminders_created: notifications.length
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})