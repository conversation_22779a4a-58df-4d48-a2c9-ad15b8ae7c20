-- Debug User Profile Issues
-- Run this in Supabase SQL Editor to check your user data

-- 1. Check if your user exists in the users table
SELECT 'Users in database:' as info;
SELECT id, name, email, role, status, created_at 
FROM users 
ORDER BY created_at DESC;

-- 2. Check if there are any data type issues
SELECT 'User table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Test a simple select with RLS (this simulates what the app is trying to do)
-- Replace 'YOUR_USER_ID_HERE' with your actual user ID from the first query
-- SELECT 'Testing RLS select:' as info;
-- SELECT * FROM users WHERE id = 'YOUR_USER_ID_HERE';

-- 4. Check current auth user (if running from authenticated context)
SELECT 'Current auth user:' as info;
SELECT auth.uid() as current_user_id;

-- 5. Test if <PERSON><PERSON> is working correctly
SELECT 'RLS test - should return your user if policies work:' as info;
SELECT id, name, email, role 
FROM users 
WHERE id = auth.uid();
