-- Row Level Security Policies for Dental Appointment Booking System
-- Run this after schema.sql in Supabase SQL Editor

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log ENABLE ROW LEVEL SECURITY;

-- Users Table Policies
-- Users can read their own profile
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile (except role)
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id
        AND role = (SELECT role FROM users WHERE id = auth.uid()) -- Can't change own role
    );

-- Staff, dentists, and admins can view all users
CREATE POLICY "Staff can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'dentist', 'admin')
        )
    );

-- Only admins can insert new users
CREATE POLICY "Admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

-- Only admins can update user roles
CREATE POLICY "Admins can update user roles" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

-- Appointments Table Policies
-- Patients can view their own appointments
CREATE POLICY "Patients can view own appointments" ON appointments
    FOR SELECT USING (
        patient_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'patient'
        )
    );

-- Patients can create their own appointments
CREATE POLICY "Patients can create own appointments" ON appointments
    FOR INSERT WITH CHECK (
        patient_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'patient'
        )
    );

-- Patients can update their own appointments (limited)
CREATE POLICY "Patients can update own appointments" ON appointments
    FOR UPDATE USING (
        patient_id = auth.uid()
        AND status = 'scheduled' -- Can only modify scheduled appointments
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'patient'
        )
    );

-- Staff can view all appointments
CREATE POLICY "Staff can view all appointments" ON appointments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

-- Staff can create appointments for any patient
CREATE POLICY "Staff can create appointments" ON appointments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

-- Staff can update all appointments
CREATE POLICY "Staff can update appointments" ON appointments
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

-- Dentists can view appointments assigned to them
CREATE POLICY "Dentists can view own appointments" ON appointments
    FOR SELECT USING (
        dentist_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'dentist'
        )
    );

-- Dentists can update appointment notes for their appointments
CREATE POLICY "Dentists can update appointment notes" ON appointments
    FOR UPDATE USING (
        dentist_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'dentist'
        )
    )
    WITH CHECK (
        dentist_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'dentist'
        )
    );

-- Notifications Table Policies
-- Users can view their own notifications
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

-- Staff and admins can view all notifications
CREATE POLICY "Staff can view all notifications" ON notifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

-- System can insert notifications for any user (for automation)
CREATE POLICY "System can create notifications" ON notifications
    FOR INSERT WITH CHECK (true);

-- Admin Activity Log Policies
-- Only admins can view admin activity logs
CREATE POLICY "Admins can view activity logs" ON admin_activity_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

-- Only admins can create activity log entries
CREATE POLICY "Admins can create activity logs" ON admin_activity_log
    FOR INSERT WITH CHECK (
        admin_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );