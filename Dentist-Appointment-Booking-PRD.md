## Project Overview

A cross-platform (mobile and web) appointment booking app for a dental clinic, with role-based access for patients, staff, dentists, and admins. All interfaces use a unified Supabase backend for authentication, storage, and real-time sync.

***

## Goals

- Seamless booking and management of appointments across mobile and web.
- Distinct interfaces, permissions, and workflows for four roles.
- Automated notifications and follow-up handling.
- Developer/admin access for ongoing configuration and maintenance.

***

## User Roles

| Role | Capabilities |
| :-- | :-- |
| Patient | Book appointments, view/edit their own, receive notifications |
| Staff | Book/manage for any patient, edit all appointments, assign dentists, add follow-ups |
| Dentist | View schedule, add notes, schedule follow-up appointments |
| **Admin** | Manage all users, roles, appointments, system settings, logs, notifications, and app functions |


***

## Functional Requirements

### Authentication & Authorization

- User authentication via email/password or OTP (Supabase Auth).
- Role assignment and enforcement at login.
- Admin-only sections protected by strict access policies.

### Appointment Management

- **Booking:**
    - Calendar-based date/time selection, dentist assignment, conflict prevention.
- **Timings:**
    - Monday to Saturday 10am to 6pm
- **Slots:**
    - 1 hour Slots for Appointments
- **Viewing:**
    - Patients: Own upcoming/past appointments.
    - Staff/Dentists/Admins: Complete schedule dashboard, filter by multiple criteria.
- **Editing:**
    - Reschedule/cancel (patients), global modify (staff/admin).
- **Follow-up:**
    - Tied to historical appointments, tracked and scheduled by staff/dentist/admin.

### Notification System

- **Reminders:**
    - Push notification 24 hours prior (mobile: Expo, web: Firebase).
    - Email fallback if required.
- **Updates:**
    - Booking/cancellation confirmations; admin can configure templates/delivery timings.

### Real-Time Data Sync

- All CRUD actions propagate instantly via Supabase subscriptions.

### Calendar Integration

- Month/week agenda views.
- Optional external calendar sync.
- Search, filter by dentist, patient, status.

***

## Admin Features

### User & Role Management

- Create, modify, or deactivate any user or role, including staff and dentists.
- Reset passwords, assign/revoke permissions.
- Audit user activity via logs.

### Appointment Oversight

- Global view and edit rights over all bookings and follow-ups.
- Manual correction of appointment data.
- Override/cancel appointments as operationally needed.

### System Settings

- Configure notification timing, templates, and channels.
- Enable/disable features such as booking limits and rescheduling policies.
- Perform system maintenance, batch operations, and data clean-ups.

### Logs & Monitoring

- Access usage, security, notification logs.
- Re-trigger or repair failed notifications.

### Function Setup

- Create/manage Supabase Edge Functions for scheduling, notifications, automation.
- Developer console for custom scripts and configurations.

***

## Non-Functional Requirements

- Fully responsive and touch-friendly UI.
- Accessibility as per WCAG standards.
- Secure, encrypted data storage and transmission.
- Audit trails for all admin-sensitive operations.

***

## Technical Specifications

| Component | Technology |
| :-- | :-- |
| Mobile UI | React Native with Expo |
| Web UI | React + Vite |
| Backend/DB | Supabase (Postgres, Auth, Edge Functions) |
| Notifications | Expo Push, Firebase Cloud Messaging |
| Real-Time Sync | Supabase Realtime Subscriptions |

***

## Data Model Outline

### Users Table

- id, name, email, phone, role (patient, staff, dentist, admin), password_hash, status

### Appointments Table

- id, patient_id, staff_id, dentist_id, date, start_time, end_time, notes, status, followup_id, created_at

### Notifications Table

- id, user_id, appointment_id, type, sent_at, status

### Admin Activity Log Table

- id, admin_id, action_type, target_id, timestamp, description

***

## User Stories

- **Patient:** Book/check/modify appointments, get reminders.
- **Staff:** Manage all bookings, assign dentists, add follow-ups.
- **Dentist:** Review schedule, add notes, create follow-up appointments.
- **Admin:** Oversee users, appointments, configuration, notifications, logs, and maintenance.

***

## Acceptance Criteria

- Secure authentication, strict role-based access.
- Real-time updates across mobile and web clients.
- All users access only allowed functions.
- Admin can manage all system aspects and see change/audit logs.
- Automated, customizable reminders and confirmations.
- App works reliably on iOS, Android, Chrome, and Safari.

***

## Milestones

1. Database schema, roles, and Supabase setup
2. Authentication, role enforcement, and UI scaffolding
3. Appointment booking, calendar, and notification workflows
4. Real-time sync and multi-role dashboards
5. Admin management features and audit logs
6. Security, accessibility, and release QA

***

This **comprehensive PRD** supports a scalable, secure, and maintainable system suitable for development with AI coding tools, including full developer/admin operational control.

