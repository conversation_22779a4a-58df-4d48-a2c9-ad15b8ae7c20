<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Signup</h1>
    <div id="result"></div>
    
    <script>
        const supabaseUrl = 'https://zpqsxittdililrkftyqj.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpwcXN4aXR0ZGlsaWxya2Z0eXFqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTk0MjYsImV4cCI6MjA2NzM5NTQyNn0.qoaJAOfYQ_TeE9kRWxR2cAzPWyo7E5VnE60cYx5clw8';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);
        
        async function testSignup() {
            try {
                console.log('Testing signup...');
                
                // Test signup
                const { data, error } = await supabase.auth.signUp({
                    email: '<EMAIL>',
                    password: 'testpassword123'
                });
                
                console.log('Signup result:', { data, error });
                
                if (error) {
                    document.getElementById('result').innerHTML = `<p style="color: red;">Signup Error: ${error.message}</p>`;
                    return;
                }
                
                // Test creating user profile
                if (data.user) {
                    const { error: profileError } = await supabase
                        .from('users')
                        .insert({
                            id: data.user.id,
                            name: 'Test User',
                            email: '<EMAIL>',
                            role: 'patient'
                        });
                    
                    console.log('Profile creation result:', { profileError });
                    
                    if (profileError) {
                        document.getElementById('result').innerHTML = `<p style="color: orange;">Profile Error: ${profileError.message}</p>`;
                    } else {
                        document.getElementById('result').innerHTML = `<p style="color: green;">Success! User created and profile added.</p>`;
                    }
                } else {
                    document.getElementById('result').innerHTML = `<p style="color: orange;">Signup successful but no user data returned</p>`;
                }
                
            } catch (err) {
                console.error('Test error:', err);
                document.getElementById('result').innerHTML = `<p style="color: red;">Test Error: ${err.message}</p>`;
            }
        }
        
        // Run test on page load
        testSignup();
    </script>
</body>
</html>
