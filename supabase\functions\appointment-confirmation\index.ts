import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the appointment data from the webhook payload
    const { record: appointment, old_record } = await req.json()

    // Only send confirmation for new appointments or status changes
    if (!appointment || (old_record && old_record.status === appointment.status)) {
      return new Response(
        JSON.stringify({ success: true, message: 'No confirmation needed' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get patient details
    const { data: patient, error: patientError } = await supabaseClient
      .from('users')
      .select('name, email')
      .eq('id', appointment.patient_id)
      .single()

    if (patientError) {
      throw patientError
    }

    // Get dentist details
    const { data: dentist, error: dentistError } = await supabaseClient
      .from('users')
      .select('name')
      .eq('id', appointment.dentist_id)
      .single()

    if (dentistError) {
      throw dentistError
    }

    // Create confirmation notification
    const message = `Appointment confirmed for ${appointment.date} at ${appointment.start_time} with Dr. ${dentist.name}`

    const { error: insertError } = await supabaseClient
      .from('notifications')
      .insert({
        user_id: appointment.patient_id,
        appointment_id: appointment.id,
        type: 'confirmation',
        message: message,
        status: 'pending'
      })

    if (insertError) {
      throw insertError
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Confirmation notification created'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})