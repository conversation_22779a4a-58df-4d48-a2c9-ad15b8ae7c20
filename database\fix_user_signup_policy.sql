-- Fix for user signup RLS policy
-- Run this in your Supabase SQL Editor to allow user self-registration

-- First, drop the existing restrictive policy
DROP POLICY IF EXISTS "Admins can create users" ON users;

-- Create a policy that allows users to create their own profile during signup
CREATE POLICY "Users can create own profile" ON users
    FOR INSERT WITH CHECK (
        auth.uid() = id
        AND role = 'patient' -- New users default to patient role
    );

-- Re-create the admin policy for creating users with other roles
CREATE POLICY "Admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );
