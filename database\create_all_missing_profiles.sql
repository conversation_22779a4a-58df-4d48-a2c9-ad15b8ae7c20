-- Create profiles for ALL users who exist in auth.users but not in public.users
-- This will fix the current issue and prevent future ones

-- First, let's see what users exist in auth but not in public
SELECT 'Users in auth.users but missing from public.users:' as info;
SELECT 
    au.id,
    au.email,
    au.created_at as auth_created_at,
    au.email_confirmed_at
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL
  AND au.email IS NOT NULL
ORDER BY au.created_at DESC;

-- Create profiles for all missing users
INSERT INTO public.users (id, name, email, role, status)
SELECT 
    au.id,
    COALESCE(
        au.raw_user_meta_data->>'name',
        au.raw_user_meta_data->>'full_name', 
        split_part(au.email, '@', 1)
    ) as name,
    au.email,
    'patient' as role,
    'active' as status
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL
  AND au.email IS NOT NULL
  AND au.email_confirmed_at IS NOT NULL; -- Only confirmed users

-- Verify all users now have profiles
SELECT 'All users now in public.users:' as info;
SELECT 
    pu.id, 
    pu.name, 
    pu.email, 
    pu.role, 
    pu.status, 
    pu.created_at,
    au.email_confirmed_at
FROM public.users pu
JOIN auth.users au ON pu.id = au.id
ORDER BY pu.created_at DESC;
