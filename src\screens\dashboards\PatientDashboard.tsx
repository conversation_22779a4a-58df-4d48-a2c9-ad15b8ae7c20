import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Card, Text, Button, Avatar } from 'react-native-paper';
import { useAuth } from '../../contexts/AuthContext';

export const PatientDashboard: React.FC = () => {
  const { profile, signOut } = useAuth();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Avatar.Text size={64} label={profile?.name?.charAt(0) || 'P'} />
        <Text variant="headlineSmall" style={styles.welcome}>
          Welcome, {profile?.name}
        </Text>
        <Text variant="bodyLarge" style={styles.role}>
          Patient Portal
        </Text>
      </View>

      <View style={styles.content}>
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge">Upcoming Appointments</Text>
            <Text variant="bodyMedium" style={styles.emptyText}>
              No upcoming appointments
            </Text>
            <Button mode="contained" style={styles.button}>
              Book New Appointment
            </Button>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge">Recent Appointments</Text>
            <Text variant="bodyMedium" style={styles.emptyText}>
              No recent appointments
            </Text>
            <Button mode="outlined" style={styles.button}>
              View History
            </Button>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge">Notifications</Text>
            <Text variant="bodyMedium" style={styles.emptyText}>
              No new notifications
            </Text>
          </Card.Content>
        </Card>
      </View>

      <View style={styles.actions}>
        <Button mode="outlined" onPress={signOut} style={styles.signOutButton}>
          Sign Out
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#1976d2',
  },
  welcome: {
    marginTop: 16,
    color: 'white',
    fontWeight: 'bold',
  },
  role: {
    marginTop: 4,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  content: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  emptyText: {
    marginVertical: 16,
    color: '#666',
  },
  button: {
    marginTop: 8,
  },
  actions: {
    padding: 16,
    paddingBottom: 32,
  },
  signOutButton: {
    borderColor: '#d32f2f',
  },
});