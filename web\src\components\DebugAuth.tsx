import React, { useEffect, useState } from 'react';
import { Box, Typography, Button, Paper, Alert } from '@mui/material';
import { supabase } from '../lib/supabase';

export const DebugAuth: React.FC = () => {
  const [authState, setAuthState] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      console.log('Checking auth state...');
      
      // Check current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      console.log('Session:', { session, sessionError });

      if (session?.user) {
        // Try to fetch user profile
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();
        
        console.log('Profile:', { profile, profileError });
        
        setAuthState({
          session,
          sessionError,
          profile,
          profileError,
          user: session.user
        });
      } else {
        setAuthState({
          session,
          sessionError,
          profile: null,
          profileError: null,
          user: null
        });
      }
    } catch (error) {
      console.error('Debug auth error:', error);
      setAuthState({
        error: error
      });
    } finally {
      setLoading(false);
    }
  };

  const clearSession = async () => {
    await supabase.auth.signOut();
    window.location.reload();
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Checking authentication state...</Typography>
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Auth Debug Information
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Button variant="contained" onClick={checkAuthState} sx={{ mr: 2 }}>
          Refresh Auth State
        </Button>
        <Button variant="outlined" color="error" onClick={clearSession}>
          Clear Session & Reload
        </Button>
      </Box>

      {authState?.error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error: {authState.error.message}
        </Alert>
      )}

      <Box sx={{ mb: 2 }}>
        <Typography variant="h6">Session Status:</Typography>
        <Typography variant="body2" component="pre" sx={{ bgcolor: 'grey.100', p: 1, borderRadius: 1 }}>
          {JSON.stringify({
            hasSession: !!authState?.session,
            hasUser: !!authState?.user,
            userId: authState?.user?.id,
            userEmail: authState?.user?.email,
            sessionError: authState?.sessionError?.message
          }, null, 2)}
        </Typography>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="h6">Profile Status:</Typography>
        <Typography variant="body2" component="pre" sx={{ bgcolor: 'grey.100', p: 1, borderRadius: 1 }}>
          {JSON.stringify({
            hasProfile: !!authState?.profile,
            profileData: authState?.profile,
            profileError: authState?.profileError?.message
          }, null, 2)}
        </Typography>
      </Box>
    </Paper>
  );
};
