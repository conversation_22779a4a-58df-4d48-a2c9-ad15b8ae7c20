# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-08-25 10:11:33 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

* Cross-platform dental appointment system with role-based access (patient, staff, dentist, admin)

## Key Features

* Supabase-backed real-time sync
* Automated notifications (Expo/Firebase)
* Admin control panel for system management
* Multi-role calendar interfaces

## Overall Architecture

* React Native (mobile) + Vite/React (web) frontends
* Supabase backend (Postgres, Auth, Realtime)
* Expo Push/FCM for notifications