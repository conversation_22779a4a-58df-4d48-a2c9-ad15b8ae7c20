-- Fix Profile Update Issue
-- Run this in Supabase SQL Editor to allow users to update their profiles

-- Drop and recreate the user update policy
DROP POLICY IF EXISTS "Users can update own profile" ON users;

-- Create a more permissive policy for users to update their own profiles
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE 
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Also ensure users can select their own profile (needed for the update to work)
DROP POLICY IF EXISTS "Users can view own profile" ON users;
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT 
    USING (auth.uid() = id);

-- Test the policy by checking if it exists
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'users' 
AND policyname IN ('Users can update own profile', 'Users can view own profile');
