import React, { useState } from 'react';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Box,
  Chip,
} from '@mui/material';
import {
  CalendarToday,
  Schedule,
  Notifications,
  Person,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { ProfileEdit } from '../ProfileEdit';
import { ChangePassword } from '../ChangePassword';

export const PatientDashboard: React.FC = () => {
  const { profile, signOut } = useAuth();
  const [profileEditOpen, setProfileEditOpen] = useState(false);
  const [changePasswordOpen, setChangePasswordOpen] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{ width: 64, height: 64, mr: 2, bgcolor: 'primary.main' }}
          >
            {profile?.name?.charAt(0) || 'P'}
          </Avatar>
          <Box>
            <Typography variant="h4" component="h1">
              Welcome back, {profile?.name}
            </Typography>
            <Chip
              label="Patient Portal"
              color="primary"
              variant="outlined"
              sx={{ mt: 1 }}
            />
          </Box>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CalendarToday sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Today's Appointments</Typography>
              </Box>
              <Typography variant="h3" color="primary">0</Typography>
              <Typography variant="body2" color="text.secondary">
                No appointments scheduled
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Schedule sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">Upcoming Appointments</Typography>
              </Box>
              <Typography variant="h3" color="success.main">0</Typography>
              <Typography variant="body2" color="text.secondary">
                No upcoming appointments
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Notifications sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">Notifications</Typography>
              </Box>
              <Typography variant="h3" color="warning.main">0</Typography>
              <Typography variant="body2" color="text.secondary">
                No new notifications
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Cards */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <CalendarToday sx={{ mr: 1, verticalAlign: 'middle' }} />
                Appointment Management
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Book new appointments, view your schedule, and manage existing bookings.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button variant="contained" fullWidth>
                  Book New Appointment
                </Button>
                <Button variant="outlined" fullWidth>
                  View History
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                Profile Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Update your personal information and preferences.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => setProfileEditOpen(true)}
                >
                  Edit Profile
                </Button>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => setChangePasswordOpen(true)}
                >
                  Change Password
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sign Out Button */}
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
        <Button
          variant="outlined"
          color="error"
          onClick={handleSignOut}
          sx={{ minWidth: 200 }}
        >
          Sign Out
        </Button>
      </Box>

      {/* Profile Management Dialogs */}
      <ProfileEdit
        open={profileEditOpen}
        onClose={() => setProfileEditOpen(false)}
      />
      <ChangePassword
        open={changePasswordOpen}
        onClose={() => setChangePasswordOpen(false)}
      />
    </Container>
  );
};