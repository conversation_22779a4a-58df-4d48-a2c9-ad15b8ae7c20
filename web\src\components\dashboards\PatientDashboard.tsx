import React from 'react';
import {
  Contain<PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Box,
  Chip,
} from '@mui/material';
import {
  CalendarToday,
  Schedule,
  Notifications,
  Person,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

export const PatientDashboard: React.FC = () => {
  const { profile, signOut } = useAuth();

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{ width: 64, height: 64, mr: 2, bgcolor: 'primary.main' }}
          >
            {profile?.name?.charAt(0) || 'P'}
          </Avatar>
          <Box>
            <Typography variant="h4" component="h1">
              Welcome back, {profile?.name}
            </Typography>
            <Chip
              label="Patient Portal"
              color="primary"
              variant="outlined"
              sx={{ mt: 1 }}
            />
          </Box>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CalendarToday sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Today's Appointments</Typography>
              </Box>
              <Typography variant="h3" color="primary">0</Typography>
              <Typography variant="body2" color="text.secondary">
                No appointments scheduled
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Schedule sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">Upcoming Appointments</Typography>
              </Box>
              <Typography variant="h3" color="success.main">0</Typography>
              <Typography variant="body2" color="text.secondary">
                No upcoming appointments
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Notifications sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">Notifications</Typography>
              </Box>
              <Typography variant="h3" color="warning.main">0</Typography>
              <Typography variant="body2" color="text.secondary">
                No new notifications
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Cards */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <CalendarToday sx={{ mr: 1, verticalAlign: 'middle' }} />
                Appointment Management
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Book new appointments, view your schedule, and manage existing bookings.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button variant="contained" fullWidth>
                  Book New Appointment
                </Button>
                <Button variant="outlined" fullWidth>
                  View History
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                Profile Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Update your personal information and preferences.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button variant="contained" fullWidth>
                  Edit Profile
                </Button>
                <Button variant="outlined" fullWidth>
                  Change Password
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sign Out Button */}
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
        <Button
          variant="outlined"
          color="error"
          onClick={signOut}
          sx={{ minWidth: 200 }}
        >
          Sign Out
        </Button>
      </Box>
    </Container>
  );
};