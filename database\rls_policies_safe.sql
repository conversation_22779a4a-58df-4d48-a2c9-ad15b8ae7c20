-- Safe Row Level Security Policies for Dental Appointment Booking System
-- This version handles existing policies gracefully
-- Run this after schema_safe.sql in Supabase SQL Editor

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts, then recreate them

-- Users Table Policies
DROP POLICY IF EXISTS "Users can view own profile" ON users;
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON users;
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id
        AND role = (SELECT role FROM users WHERE id = auth.uid()) -- Can't change own role
    );

DROP POLICY IF EXISTS "Staff can view all users" ON users;
CREATE POLICY "Staff can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'dentist', 'admin')
        )
    );

-- IMPORTANT: Allow users to create their own profile during signup
DROP POLICY IF EXISTS "Users can create own profile" ON users;
CREATE POLICY "Users can create own profile" ON users
    FOR INSERT WITH CHECK (
        auth.uid() = id
        AND role = 'patient' -- New users default to patient role
    );

DROP POLICY IF EXISTS "Admins can create users" ON users;
CREATE POLICY "Admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Admins can update user roles" ON users;
CREATE POLICY "Admins can update user roles" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

-- Appointments Table Policies
DROP POLICY IF EXISTS "Patients can view own appointments" ON appointments;
CREATE POLICY "Patients can view own appointments" ON appointments
    FOR SELECT USING (
        patient_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'patient'
        )
    );

DROP POLICY IF EXISTS "Patients can create own appointments" ON appointments;
CREATE POLICY "Patients can create own appointments" ON appointments
    FOR INSERT WITH CHECK (
        patient_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'patient'
        )
    );

DROP POLICY IF EXISTS "Patients can update own appointments" ON appointments;
CREATE POLICY "Patients can update own appointments" ON appointments
    FOR UPDATE USING (
        patient_id = auth.uid()
        AND status = 'scheduled' -- Can only modify scheduled appointments
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'patient'
        )
    );

DROP POLICY IF EXISTS "Staff can view all appointments" ON appointments;
CREATE POLICY "Staff can view all appointments" ON appointments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

DROP POLICY IF EXISTS "Staff can create appointments" ON appointments;
CREATE POLICY "Staff can create appointments" ON appointments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

DROP POLICY IF EXISTS "Staff can update appointments" ON appointments;
CREATE POLICY "Staff can update appointments" ON appointments
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

DROP POLICY IF EXISTS "Dentists can view own appointments" ON appointments;
CREATE POLICY "Dentists can view own appointments" ON appointments
    FOR SELECT USING (
        dentist_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'dentist'
        )
    );

DROP POLICY IF EXISTS "Dentists can update appointment notes" ON appointments;
CREATE POLICY "Dentists can update appointment notes" ON appointments
    FOR UPDATE USING (
        dentist_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'dentist'
        )
    )
    WITH CHECK (
        dentist_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'dentist'
        )
    );

-- Notifications Table Policies
DROP POLICY IF EXISTS "Users can view own notifications" ON notifications;
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Staff can view all notifications" ON notifications;
CREATE POLICY "Staff can view all notifications" ON notifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('staff', 'admin')
        )
    );

DROP POLICY IF EXISTS "System can create notifications" ON notifications;
CREATE POLICY "System can create notifications" ON notifications
    FOR INSERT WITH CHECK (true);

-- Admin Activity Log Policies
DROP POLICY IF EXISTS "Admins can view activity logs" ON admin_activity_log;
CREATE POLICY "Admins can view activity logs" ON admin_activity_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Admins can create activity logs" ON admin_activity_log;
CREATE POLICY "Admins can create activity logs" ON admin_activity_log
    FOR INSERT WITH CHECK (
        admin_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role = 'admin'
        )
    );
