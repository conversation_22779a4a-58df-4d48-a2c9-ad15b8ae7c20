import { MD3LightTheme as DefaultTheme } from 'react-native-paper';

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#1e3a8a', // Dark Blue
    accent: '#22b0e6',  // <PERSON>an
    secondary: '#f59e0b', // Yellow-Orange
    background: '#fff',
    surface: '#fff',
    text: '#222', // Soft black
    onSurface: '#333', // Slightly lighter black
    placeholder: '#888',
    error: '#d10000', // Red
    notification: '#ff9800', // Orange
    danger: '#d10000', // Red for errors
    success: '#009900', // Green for success messages
    smoke: '#f3f3f3', // Light gray for backgrounds
    lightGray: '#f0f0f0', // Light gray for surfaces
    // Add more custom colors as needed
  },
};

export default theme; 