import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useAuth, UserRole } from '../contexts/AuthContext';
import { LoginScreen } from '../screens/auth/LoginScreen';

// Import dashboard screens (we'll create these next)
import { PatientDashboard } from '../screens/dashboards/PatientDashboard';
import { StaffDashboard } from '../screens/dashboards/StaffDashboard';
import { DentistDashboard } from '../screens/dashboards/DentistDashboard';
import { AdminDashboard } from '../screens/dashboards/AdminDashboard';

// Placeholder components for now
const PlaceholderScreen: React.FC<{ title: string }> = ({ title }) => (
  <></> // We'll implement these properly later
);

export type RootStackParamList = {
  Login: undefined;
  PatientTabs: undefined;
  StaffTabs: undefined;
  DentistTabs: undefined;
  AdminTabs: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

// Patient Tab Navigator
const PatientTab = createBottomTabNavigator();
const PatientTabNavigator = () => (
  <PatientTab.Navigator>
    <PatientTab.Screen name="Dashboard" component={PatientDashboard} />
    <PatientTab.Screen name="Appointments" component={PlaceholderScreen} />
    <PatientTab.Screen name="Profile" component={PlaceholderScreen} />
  </PatientTab.Navigator>
);

// Staff Tab Navigator
const StaffTab = createBottomTabNavigator();
const StaffTabNavigator = () => (
  <StaffTab.Navigator>
    <StaffTab.Screen name="Dashboard" component={StaffDashboard} />
    <StaffTab.Screen name="Appointments" component={PlaceholderScreen} />
    <StaffTab.Screen name="Patients" component={PlaceholderScreen} />
    <StaffTab.Screen name="Profile" component={PlaceholderScreen} />
  </StaffTab.Navigator>
);

// Dentist Tab Navigator
const DentistTab = createBottomTabNavigator();
const DentistTabNavigator = () => (
  <DentistTab.Navigator>
    <DentistTab.Screen name="Dashboard" component={DentistDashboard} />
    <DentistTab.Screen name="Schedule" component={PlaceholderScreen} />
    <DentistTab.Screen name="Patients" component={PlaceholderScreen} />
    <DentistTab.Screen name="Profile" component={PlaceholderScreen} />
  </DentistTab.Navigator>
);

// Admin Tab Navigator
const AdminTab = createBottomTabNavigator();
const AdminTabNavigator = () => (
  <AdminTab.Navigator>
    <AdminTab.Screen name="Dashboard" component={AdminDashboard} />
    <AdminTab.Screen name="Users" component={PlaceholderScreen} />
    <AdminTab.Screen name="Appointments" component={PlaceholderScreen} />
    <AdminTab.Screen name="Settings" component={PlaceholderScreen} />
    <AdminTab.Screen name="Logs" component={PlaceholderScreen} />
  </AdminTab.Navigator>
);

export const AppNavigator: React.FC = () => {
  const { user, profile, loading } = useAuth();

  if (loading) {
    // You might want to create a proper loading screen
    return null;
  }

  if (!user || !profile) {
    return (
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Login" component={LoginScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }

  const getTabNavigator = (role: UserRole) => {
    switch (role) {
      case 'patient':
        return <PatientTabNavigator />;
      case 'staff':
        return <StaffTabNavigator />;
      case 'dentist':
        return <DentistTabNavigator />;
      case 'admin':
        return <AdminTabNavigator />;
      default:
        return <PatientTabNavigator />;
    }
  };

  return (
    <NavigationContainer>
      {getTabNavigator(profile.role)}
    </NavigationContainer>
  );
};