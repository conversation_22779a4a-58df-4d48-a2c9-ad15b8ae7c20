-- Fix Mobile App RLS Recursion Error
-- This fixes the infinite recursion in RLS policies
-- Run this in Supabase SQL Editor

-- Drop the problematic policies that cause recursion
DROP POLICY IF EXISTS "staff_select_all" ON users;
DROP POLICY IF EXISTS "admin_insert_any" ON users;
DROP POLICY IF EXISTS "admin_update_any" ON users;

-- Keep only the essential, non-recursive policies for now
-- These are sufficient for basic functionality

-- 1. Users can view their own profile (no recursion)
-- Already exists: users_select_own

-- 2. Users can update their own profile (no recursion)  
-- Already exists: users_update_own

-- 3. Users can create their own profile during signup (no recursion)
-- Already exists: users_insert_own

-- For now, we'll handle staff/admin permissions at the application level
-- rather than in RLS policies to avoid recursion

-- Verify only the safe policies remain
SELECT 'Remaining policies after cleanup:' as info;
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY policyname;

-- Test that basic user operations work without recursion
SELECT 'Testing basic user select (should work):' as info;
SELECT COUNT(*) as user_count FROM users WHERE id = auth.uid();
