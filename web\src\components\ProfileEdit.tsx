import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Alert,
  Box,
  CircularProgress,
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';

interface ProfileEditProps {
  open: boolean;
  onClose: () => void;
}

export const ProfileEdit: React.FC<ProfileEditProps> = ({ open, onClose }) => {
  const { profile, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Form state
  const [formData, setFormData] = useState({
    name: profile?.name || '',
    phone: profile?.phone || '',
  });

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      console.log('Updating profile for user:', user.id);
      console.log('Update data:', {
        name: formData.name.trim(),
        phone: formData.phone.trim() || null,
      });

      const { data, error: updateError } = await supabase
        .from('users')
        .update({
          name: formData.name.trim(),
          phone: formData.phone.trim() || null,
        })
        .eq('id', user.id)
        .select(); // Add select to get the updated data

      console.log('Update result:', { data, updateError });

      if (updateError) {
        console.error('Update error details:', updateError);
        setError(`Failed to update profile: ${updateError.message}`);
      } else {
        setSuccess('Profile updated successfully!');
        // Close dialog after a short delay
        setTimeout(() => {
          onClose();
          setSuccess('');
        }, 1500);
      }
    } catch (err: any) {
      console.error('Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError('');
      setSuccess('');
      // Reset form to current profile data
      setFormData({
        name: profile?.name || '',
        phone: profile?.phone || '',
      });
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Edit Profile</DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <TextField
            fullWidth
            label="Full Name"
            value={formData.name}
            onChange={handleChange('name')}
            margin="normal"
            required
            disabled={loading}
          />

          <TextField
            fullWidth
            label="Email Address"
            value={profile?.email || ''}
            margin="normal"
            disabled
            helperText="Email cannot be changed"
          />

          <TextField
            fullWidth
            label="Phone Number"
            value={formData.phone}
            onChange={handleChange('phone')}
            margin="normal"
            placeholder="(optional)"
            disabled={loading}
          />

          <TextField
            fullWidth
            label="Role"
            value={profile?.role || ''}
            margin="normal"
            disabled
            helperText="Role cannot be changed"
          />
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading || !formData.name.trim()}
          >
            {loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} />
                Saving...
              </Box>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
