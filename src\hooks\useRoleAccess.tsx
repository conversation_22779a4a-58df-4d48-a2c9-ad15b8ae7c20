import { useMemo } from 'react';
import { useAuth, UserRole } from '../contexts/AuthContext';

// Define permission sets for each role
const rolePermissions = {
  patient: [
    'view_own_appointments',
    'book_appointments',
    'edit_own_appointments',
    'view_own_profile',
    'receive_notifications'
  ],
  staff: [
    'view_all_appointments',
    'book_appointments_for_patients',
    'edit_all_appointments',
    'manage_patient_records',
    'assign_dentists',
    'schedule_followups',
    'view_all_profiles',
    'send_notifications'
  ],
  dentist: [
    'view_assigned_appointments',
    'add_appointment_notes',
    'view_patient_history',
    'schedule_followups',
    'view_own_profile'
  ],
  admin: [
    'manage_users',
    'manage_roles',
    'view_all_appointments',
    'edit_all_appointments',
    'system_configuration',
    'view_audit_logs',
    'manage_notifications',
    'system_maintenance',
    'override_appointments',
    'delete_records'
  ]
};

export interface PermissionCheck {
  hasPermission: boolean;
  userRole: UserRole | null;
  isLoading: boolean;
}

export const useRoleAccess = () => {
  const { profile, loading } = useAuth();

  const checkPermission = (permission: string): PermissionCheck => {
    if (loading) {
      return { hasPermission: false, userRole: null, isLoading: true };
    }

    if (!profile) {
      return { hasPermission: false, userRole: null, isLoading: false };
    }

    const userPermissions = rolePermissions[profile.role] || [];
    const hasPermission = userPermissions.includes(permission);

    return {
      hasPermission,
      userRole: profile.role,
      isLoading: false
    };
  };

  const checkMultiplePermissions = (permissions: string[]): PermissionCheck => {
    if (loading) {
      return { hasPermission: false, userRole: null, isLoading: true };
    }

    if (!profile) {
      return { hasPermission: false, userRole: null, isLoading: false };
    }

    const userPermissions = rolePermissions[profile.role] || [];
    const hasPermission = permissions.every(permission =>
      userPermissions.includes(permission)
    );

    return {
      hasPermission,
      userRole: profile.role,
      isLoading: false
    };
  };

  const checkRole = (requiredRole: UserRole | UserRole[]): PermissionCheck => {
    if (loading) {
      return { hasPermission: false, userRole: null, isLoading: true };
    }

    if (!profile) {
      return { hasPermission: false, userRole: null, isLoading: false };
    }

    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    const hasPermission = roles.includes(profile.role);

    return {
      hasPermission,
      userRole: profile.role,
      isLoading: false
    };
  };

  const getUserPermissions = (): string[] => {
    if (!profile) return [];
    return rolePermissions[profile.role] || [];
  };

  const canAccessAdminPanel = (): PermissionCheck => {
    return checkRole('admin');
  };

  const canManageAppointments = (): PermissionCheck => {
    return checkMultiplePermissions(['edit_all_appointments']);
  };

  const canViewAllUsers = (): PermissionCheck => {
    return checkPermission('view_all_profiles');
  };

  return {
    profile,
    loading,
    checkPermission,
    checkMultiplePermissions,
    checkRole,
    getUserPermissions,
    canAccessAdminPanel,
    canManageAppointments,
    canViewAllUsers,
    // Utility functions for common checks
    isPatient: profile?.role === 'patient',
    isStaff: profile?.role === 'staff',
    isDentist: profile?.role === 'dentist',
    isAdmin: profile?.role === 'admin',
  };
};