-- Check if specific user exists in database
-- Replace the user ID with the one from your mobile app logs: 16387a85-3887-4e4a-b010-38a9545941a2

-- Check if user exists in auth.users
SELECT 'User in auth.users:' as info;
SELECT id, email, created_at, email_confirmed_at
FROM auth.users 
WHERE id = '16387a85-3887-4e4a-b010-38a9545941a2';

-- Check if user exists in public.users
SELECT 'User in public.users:' as info;
SELECT id, name, email, role, status, created_at
FROM public.users 
WHERE id = '16387a85-3887-4e4a-b010-38a9545941a2';

-- Check all users in public.users to see what's there
SELECT 'All users in public.users:' as info;
SELECT id, name, email, role, status, created_at
FROM public.users 
ORDER BY created_at DESC;

-- Test RLS policy for this specific user
-- This simulates what the mobile app is trying to do
SELECT 'Testing RLS policy for this user:' as info;
-- Note: This will only work if you run it while authenticated as this user
-- SELECT * FROM public.users WHERE id = auth.uid();
